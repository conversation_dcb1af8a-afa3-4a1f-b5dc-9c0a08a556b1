{"name": "gin-vue-admin", "version": "2.8.2", "private": true, "scripts": {"serve": "node openDocument.js && vite --host --mode development", "build": "vite build --mode production", "limit-build": "npm install increase-memory-limit-fixbug cross-env -g && npm run fix-memory-limit && node ./limit && npm run build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.10", "@vue-office/docx": "^1.6.2", "@vue-office/excel": "^1.7.11", "@vue-office/pdf": "^2.0.2", "@vueuse/core": "^11.0.3", "@vueuse/integrations": "^12.0.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ace-builds": "^1.36.4", "axios": "1.8.2", "chokidar": "^4.0.0", "core-js": "^3.38.1", "echarts": "5.5.1", "element-plus": "^2.10.2", "highlight.js": "^11.10.0", "hls.js": "^1.6.8", "install": "^0.13.0", "marked": "14.1.1", "marked-highlight": "^2.1.4", "mitt": "^3.0.1", "npm": "^11.3.0", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.2.2", "qs": "^6.13.0", "screenfull": "^6.0.2", "sortablejs": "^1.15.3", "spark-md5": "^3.0.2", "tailwindcss": "^3.4.10", "universal-cookie": "^7", "vform3-builds": "^3.0.10", "vite-auto-import-svg": "^1.5.0", "vue": "^3.5.7", "vue-cropper": "^1.1.4", "vue-echarts": "^7.0.3", "vue-qr": "^4.0.9", "vue-router": "^4.4.3", "vue3-ace-editor": "^2.2.4", "vue3-sfc-loader": "^0.9.5", "vuedraggable": "^4.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.25.1", "@eslint/js": "^8.56.0", "@vitejs/plugin-legacy": "^6.0.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/cli-plugin-babel": "~5.0.8", "@vue/cli-plugin-eslint": "~5.0.8", "@vue/cli-plugin-router": "~5.0.8", "@vue/cli-plugin-vuex": "~5.0.8", "@vue/cli-service": "~5.0.8", "@vue/compiler-sfc": "^3.5.1", "autoprefixer": "^10.4.20", "babel-plugin-import": "^1.13.8", "chalk": "^5.3.0", "dotenv": "^16.4.5", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.19.2", "sass": "^1.78.0", "terser": "^5.31.6", "vite": "^6.2.3", "vite-plugin-banner": "^0.8.0", "vite-plugin-importer": "^0.2.5", "vite-plugin-vue-devtools": "^7.0.16"}}