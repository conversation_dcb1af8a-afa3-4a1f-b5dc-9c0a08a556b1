package weibo

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/weibo"
	weiboReq "github.com/flipped-aurora/gin-vue-admin/server/model/weibo/request"
)

type WeiboBlogService struct{}

// CreateWeiboBlog 创建微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) CreateWeiboBlog(ctx context.Context, weiboBlog *weibo.WeiboBlog) (err error) {
	err = global.GVA_DB.Create(weiboBlog).Error
	return err
}

// DeleteWeiboBlog 删除微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) DeleteWeiboBlog(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&weibo.WeiboBlog{}, "id = ?", ID).Error
	return err
}

// DeleteWeiboBlogByIds 批量删除微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) DeleteWeiboBlogByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]weibo.WeiboBlog{}, "id in ?", IDs).Error
	return err
}

// UpdateWeiboBlog 更新微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) UpdateWeiboBlog(ctx context.Context, weiboBlog weibo.WeiboBlog) (err error) {
	err = global.GVA_DB.Model(&weibo.WeiboBlog{}).Where("id = ?", weiboBlog.ID).Updates(&weiboBlog).Error
	return err
}

// GetWeiboBlog 根据ID获取微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) GetWeiboBlog(ctx context.Context, ID string) (weiboBlog weibo.WeiboBlog, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&weiboBlog).Error
	return
}

// GetWeiboBlogInfoList 分页获取微博记录
// Author [yourname](https://github.com/yourname)
func (weiboBlogService *WeiboBlogService) GetWeiboBlogInfoList(ctx context.Context, info weiboReq.WeiboBlogSearch) (list []weibo.WeiboBlog, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&weibo.WeiboBlog{})
	var weiboBlogs []weibo.WeiboBlog
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	if info.Blog_id != "" {
		db = db.Where("blog_id = ?", info.Blog_id)
	}
	if len(info.Publish_atRange) == 2 {
		db = db.Where("publish_at BETWEEN ? AND ? ", info.Publish_atRange[0], info.Publish_atRange[1])
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	// 按发布时间降序排序
	err = db.Order("publish_at DESC").Find(&weiboBlogs).Error
	return weiboBlogs, total, err
}
func (weiboBlogService *WeiboBlogService) GetWeiboBlogPublic(ctx context.Context) {
	// 此方法为获取数据源定义的数据
	// 请自行实现
}
