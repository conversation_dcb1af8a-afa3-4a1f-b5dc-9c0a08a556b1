
<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
      <el-form-item label="创建日期" prop="createdAtRange">
      <template #label>
        <span>
          创建日期
          <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
        </span>
      </template>

      <el-date-picker
            v-model="searchInfo.createdAtRange"
            class="w-[380px]"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
       </el-form-item>

            <el-form-item label="博客ID" prop="blog_id">
  <el-input v-model.number="searchInfo.blog_id" placeholder="搜索条件" />
</el-form-item>

            <el-form-item label="发布时间" prop="publish_at">
  <template #label>
    <span>
      发布时间
      <el-tooltip content="搜索范围是开始日期（包含）至结束日期（不包含）">
        <el-icon><QuestionFilled /></el-icon>
      </el-tooltip>
    </span>
  </template>
<el-date-picker class="w-[380px]" v-model="searchInfo.publish_atRange" type="datetimerange" range-separator="至"  start-placeholder="开始时间" end-placeholder="结束时间"></el-date-picker></el-form-item>


        <template v-if="showAllQuery">
          <!-- 将需要控制显示状态的查询条件添加到此范围内 -->
        </template>

        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
          <el-button type="success" icon="download" @click="openCrawlDialog">抓取微博数据</el-button>
          <el-button link type="primary" icon="arrow-down" @click="showAllQuery=true" v-if="!showAllQuery">展开</el-button>
          <el-button link type="primary" icon="arrow-up" @click="showAllQuery=false" v-else>收起</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-timeline-box">
      <el-card>
        <template #header>
          <div>微博时间线</div>
        </template>
        <div class="h-[calc(100vh-300px)] overflow-y-auto" @scroll="handleScroll" ref="timelineContainer">
          <el-timeline>
            <el-timeline-item
              v-for="(item, index) in timelineData"
              :key="index"
              placement="top"
            >
              <el-card class="weibo-card">
                <!-- 微博头部信息 -->
                <div class="weibo-header">
                  <div class="user-info">
                    <el-avatar
                      :src="getWeiboData(item).user?.profile_image_url"
                      :size="40"
                      class="user-avatar"
                    >
                      {{ getWeiboData(item).user?.screen_name?.charAt(0) }}
                    </el-avatar>
                    <div class="user-details">
                      <div class="user-name">
                        {{ getWeiboData(item).user?.screen_name || '未知用户' }}
                        <el-tag v-if="getWeiboData(item).user?.verified" type="warning" size="small" class="verified-tag">
                          <el-icon><StarFilled /></el-icon>
                        </el-tag>
                      </div>
                      <div class="publish-info">
                        {{ formatAbsoluteTime(item.publish_at) }}
                        <span v-if="getWeiboData(item).source" class="source">
                          来自 {{ getWeiboData(item).source.replace(/<[^>]*>/g, '').replace(/来自\s*/, '') }}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div class="header-actions">
                    <el-button type="primary" link @click="getDetails(item)">
                      <el-icon><InfoFilled /></el-icon>
                    </el-button>
                    <el-button type="danger" link @click="deleteRow(item)">
                      <el-icon><Delete /></el-icon>
                    </el-button>
                  </div>
                </div>

                <!-- 微博内容 -->
                <div class="weibo-content">
                  <div class="weibo-text" v-html="getDisplayText(item)"></div>

                  <!-- 展开按钮 -->
                  <div v-if="shouldShowExpandButton(item)" class="expand-button-container">
                    <el-button
                      type="primary"
                      link
                      size="small"
                      @click="toggleExpandText(item)"
                      :loading="item.expandLoading"
                    >
                      {{ item.expanded ? '收起' : '展开' }}
                    </el-button>
                  </div>

                  <!-- 视频展示（仅当主微博有原创视频且不是转发微博时显示） -->
                  <div v-if="getWeiboVideo(item) && !getWeiboData(item).retweeted_status" class="weibo-video">
                    <div class="video-container">
                      <el-image
                        :src="getWeiboVideo(item).cover"
                        fit="cover"
                        class="video-cover"
                      >
                        <template #error>
                          <div class="video-placeholder">
                            <el-icon><VideoPlay /></el-icon>
                            <span>视频内容</span>
                          </div>
                        </template>
                      </el-image>
                      <div class="video-overlay" @click="playVideo(getWeiboVideo(item).url)">
                        <el-icon class="play-icon"><VideoPlay /></el-icon>
                      </div>
                      <div v-if="getWeiboVideo(item).duration" class="video-duration">
                        {{ getWeiboVideo(item).duration }}
                      </div>
                      <div v-if="getWeiboVideo(item).play_count" class="video-views">
                        {{ formatCount(getWeiboVideo(item).play_count) }}次观看
                      </div>
                    </div>
                  </div>

                  <!-- 直播展示 -->
                  <div v-if="getWeiboLive(item)" class="weibo-live">
                    <div class="live-container">
                      <el-image
                        :src="getWeiboLive(item).cover"
                        fit="cover"
                        class="live-cover"
                      >
                        <template #error>
                          <div class="live-placeholder">
                            <el-icon><VideoPlay /></el-icon>
                            <span>直播内容</span>
                          </div>
                        </template>
                      </el-image>
                      <div class="live-overlay" @click="openLive(getWeiboLive(item).url, getWeiboLive(item).isEnded)">
                        <div class="live-status" :class="{
                          'live-active': getWeiboLive(item).isLive,
                          'live-ended': getWeiboLive(item).isEnded
                        }">
                          {{ getWeiboLive(item).isLive ? '直播中' : (getWeiboLive(item).isEnded ? '回放' : '直播结束') }}
                        </div>
                        <el-icon class="play-icon"><VideoPlay /></el-icon>
                        <div v-if="getWeiboLive(item).viewCount" class="live-viewers">
                          {{ formatCount(getWeiboLive(item).viewCount) }}观看
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 图片展示 -->
                  <div v-else-if="getWeiboImages(item).length > 0" class="weibo-images">
                    <el-image
                      v-for="(img, imgIndex) in getWeiboImages(item).slice(0, 9)"
                      :key="imgIndex"
                      :src="img.thumbnail"
                      :preview-src-list="getWeiboImages(item).map(i => i.large)"
                      :initial-index="imgIndex"
                      class="weibo-image"
                      fit="cover"
                      lazy
                    >
                      <template #error>
                        <div class="image-error">
                          <el-icon><Picture /></el-icon>
                          <span>图片加载失败</span>
                        </div>
                      </template>
                    </el-image>
                  </div>

                  <!-- 转发微博展示 -->
                  <div v-if="getWeiboData(item).retweeted_status" class="retweeted-weibo">
                    <div class="retweeted-header">
                      <el-avatar :src="getWeiboData(item).retweeted_status.user.profile_image_url" :size="24" />
                      <span class="retweeted-username">{{ getWeiboData(item).retweeted_status.user.screen_name }}</span>
                      <el-icon v-if="getWeiboData(item).retweeted_status.user.verified" class="verified-icon"><Select /></el-icon>
                    </div>

                    <div class="retweeted-content">
                      <div class="retweeted-text" v-html="getRetweetedDisplayText(item)"></div>

                      <!-- 转发微博展开按钮 -->
                      <div v-if="shouldShowRetweetedExpandButton(item)" class="expand-button-container">
                        <el-button
                          type="primary"
                          link
                          size="small"
                          @click="toggleRetweetedExpandText(item)"
                          :loading="item.retweetedExpandLoading"
                        >
                          {{ item.retweetedExpanded ? '收起' : '展开' }}
                        </el-button>
                      </div>

                      <!-- 转发微博的视频 -->
                      <div v-if="getRetweetedVideo(item)" class="weibo-video">
                        <div class="video-container">
                          <el-image
                            :src="getRetweetedVideo(item).cover"
                            fit="cover"
                            class="video-cover"
                          >
                            <template #error>
                              <div class="video-placeholder">
                                <el-icon><VideoPlay /></el-icon>
                                <span>视频内容</span>
                              </div>
                            </template>
                          </el-image>
                          <div class="video-overlay" @click="playVideo(getRetweetedVideo(item).url)">
                            <el-icon class="play-icon"><VideoPlay /></el-icon>
                          </div>
                          <div v-if="getRetweetedVideo(item).duration" class="video-duration">
                            {{ getRetweetedVideo(item).duration }}
                          </div>
                          <div v-if="getRetweetedVideo(item).play_count" class="video-views">
                            {{ formatCount(getRetweetedVideo(item).play_count) }}次观看
                          </div>
                        </div>
                      </div>

                      <!-- 转发微博的图片 -->
                      <div v-else-if="getWeiboImages(getWeiboData(item).retweeted_status).length > 0" class="weibo-images">
                        <el-image
                          v-for="(img, imgIndex) in getWeiboImages(getWeiboData(item).retweeted_status).slice(0, 9)"
                          :key="imgIndex"
                          :src="img.thumbnail"
                          :preview-src-list="getWeiboImages(getWeiboData(item).retweeted_status).map(i => i.large)"
                          :initial-index="imgIndex"
                          class="weibo-image"
                          fit="cover"
                          lazy
                        >
                          <template #error>
                            <div class="image-error">
                              <el-icon><Picture /></el-icon>
                              <span>图片加载失败</span>
                            </div>
                          </template>
                        </el-image>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 微博互动数据 -->
                <div class="weibo-stats">
                  <div class="stat-item">
                    <el-icon><ChatDotRound /></el-icon>
                    <span>{{ formatCount(getWeiboData(item).comments_count) }}</span>
                  </div>
                  <div class="stat-item">
                    <el-icon><Share /></el-icon>
                    <span>{{ formatCount(getWeiboData(item).reposts_count) }}</span>
                  </div>
                  <div class="stat-item">
                    <el-icon><Star /></el-icon>
                    <span>{{ formatCount(getWeiboData(item).attitudes_count) }}</span>
                  </div>
                </div>
              </el-card>
            </el-timeline-item>
          </el-timeline>
          <div v-if="loading" class="loading-container">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          <div v-if="!hasMore && timelineData.length > 0" class="no-more-data">
            没有更多数据了
          </div>
        </div>
      </el-card>
    </div>


    <el-drawer destroy-on-close :size="appStore.drawerSize" v-model="detailShow" :show-close="true" :before-close="closeDetailShow" title="查看">
            <el-descriptions :column="1" border>
                    <el-descriptions-item label="博客ID">
    {{ detailForm.blog_id }}
</el-descriptions-item>
                    <el-descriptions-item label="发布时间">
    {{ detailForm.publish_at }}
</el-descriptions-item>
                    <el-descriptions-item label="源数据">
    {{ detailForm.data }}
</el-descriptions-item>
            </el-descriptions>
        </el-drawer>

    <!-- 抓取微博数据对话框 -->
    <el-dialog v-model="crawlDialogVisible" title="抓取微博数据" width="600px" :before-close="closeCrawlDialog">
      <el-form ref="crawlFormRef" :model="crawlForm" :rules="crawlRules" label-width="120px">
        <el-form-item label="用户ID" prop="uid" required>
          <el-input
            v-model="crawlForm.uid"
            placeholder="请输入微博用户ID，例如：5515166761"
            clearable
          />
          <div class="form-tip">
            <el-text type="info" size="small">
              可以从微博用户主页URL中获取，例如：weibo.com/u/5515166761 中的 5515166761
            </el-text>
          </div>
        </el-form-item>

        <el-form-item label="起始页码" prop="startPage">
          <el-input-number
            v-model="crawlForm.startPage"
            :min="1"
            :max="10000"
            placeholder="起始页码"
          />
          <div class="form-tip">
            <el-text type="info" size="small">默认从第1页开始抓取</el-text>
          </div>
        </el-form-item>

        <el-form-item label="最大页数" prop="maxPages">
          <el-input-number
            v-model="crawlForm.maxPages"
            :min="1"
            :max="10000"
            placeholder="最大抓取页数"
          />
          <div class="form-tip">
            <el-text type="info" size="small">建议不要设置过大，避免被限制访问</el-text>
          </div>
        </el-form-item>

        <el-form-item label="起始since_id" prop="sinceId">
          <el-input
            v-model="crawlForm.sinceId"
            placeholder="可选，用于续传抓取"
            clearable
          />
          <div class="form-tip">
            <el-text type="info" size="small">用于从特定位置开始抓取，可留空</el-text>
          </div>
        </el-form-item>

        <el-form-item label="Cookies" prop="cookies">
          <el-input
            v-model="crawlForm.cookies"
            type="textarea"
            :rows="3"
            placeholder="可选，微博登录后的cookies"
            clearable
          />
          <div class="form-tip">
            <el-text type="info" size="small">
              登录微博后，从浏览器开发者工具中复制cookies，可提高抓取成功率
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeCrawlDialog">取消</el-button>
          <el-button type="primary" @click="startCrawl" :loading="crawlLoading">
            {{ crawlLoading ? '抓取中...' : '开始抓取' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 视频/直播播放器弹窗 -->
    <el-dialog
      v-model="videoPlayerVisible"
      :title="getPlayerTitle()"
      width="80%"
      :before-close="closeVideoPlayer"
      center
      class="video-player-dialog"
    >
      <div class="video-player-container">
        <video
          v-if="currentVideoUrl"
          ref="videoElement"
          controls
          autoplay
          class="video-player"
          @ended="closeVideoPlayer"
          :muted="isLiveStream"
        >
          您的浏览器不支持视频播放
        </video>
      </div>
    </el-dialog>

  </div>
</template>

<script setup>
import {
  deleteWeiboBlog,
  findWeiboBlog,
  getWeiboBlogList,
  crawlWeiboData,
  fetchLongText
} from '@/api/weibo/weiboBlog'

// 全量引入格式化工具 请按需保留
import { formatDate } from '@/utils/format'
import { formatTimeToStr } from '@/utils/date'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Loading, InfoFilled, QuestionFilled, StarFilled, Delete, ChatDotRound, Share, Star, VideoPlay, Picture } from '@element-plus/icons-vue'
import { ref, nextTick } from 'vue'
import { useAppStore } from "@/pinia"
import Hls from 'hls.js'

defineOptions({
    name: 'WeiboBlog'
})

const appStore = useAppStore()

// 控制更多查询条件显示/隐藏状态
const showAllQuery = ref(false)

const elSearchFormRef = ref()
const timelineContainer = ref()
const videoElement = ref()

// =========== 时间线控制部分 ===========
const page = ref(1)
const pageSize = ref(20) // 每次加载20条数据
const timelineData = ref([])
const searchInfo = ref({})
const loading = ref(false)
const hasMore = ref(true)

// 重置
const onReset = () => {
  searchInfo.value = {}
  resetTimeline()
}

// 搜索
const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    resetTimeline()
  })
}

// 重置时间线数据
const resetTimeline = () => {
  page.value = 1
  timelineData.value = []
  hasMore.value = true
  getTimelineData()
}

// 获取时间线数据
const getTimelineData = async() => {
  if (loading.value || !hasMore.value) return

  loading.value = true
  try {
    const table = await getWeiboBlogList({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
    if (table.code === 0) {
      const newData = initializeItemStates(table.data.list.map(item => ({
        ...item,
        timestamp: formatTimeToStr(item.publish_at, 'yyyy-MM-dd')
      })))

      if (newData.length === 0) {
        hasMore.value = false
      } else {
        timelineData.value.push(...newData)
        page.value++

        // 如果返回的数据少于pageSize，说明没有更多数据了
        if (newData.length < pageSize.value) {
          hasMore.value = false
        }
      }
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 滚动加载
const handleScroll = (event) => {
  const { scrollTop, scrollHeight, clientHeight } = event.target
  // 当滚动到底部附近时加载更多数据
  if (scrollHeight - scrollTop - clientHeight < 100) {
    getTimelineData()
  }
}

// 获取微博数据
const getWeiboData = (item) => {
  try {
    const data = typeof item.data === 'string' ? JSON.parse(item.data) : item.data
    return data || {}
  } catch (e) {
    console.error('解析微博数据失败:', e)
    return {}
  }
}

// 格式化时间显示
const formatRelativeTime = (dateStr, useAbsolute = false) => {
  if (!dateStr) return ''

  let date

  // 处理微博API的时间格式: "Mon Aug 04 16:29:36 +0800 2025"
  if (typeof dateStr === 'string' && dateStr.includes('+')) {
    date = new Date(dateStr)
  } else {
    date = new Date(dateStr)
  }

  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return dateStr // 如果无法解析，返回原始字符串
  }

  // 如果是publish_at时间或者指定使用绝对时间，直接返回格式化的绝对时间
  if (useAbsolute) {
    return formatDate(dateStr)
  }

  const now = new Date()
  const diff = now - date

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 1) return '刚刚'
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  if (days < 7) return `${days}天前`

  return formatDate(dateStr)
}

// 格式化绝对时间（用于publish_at）
const formatAbsoluteTime = (dateStr) => {
  if (!dateStr) return ''
  return formatDate(dateStr)
}

// 格式化微博文本
const formatWeiboText = (text) => {
  if (!text) return ''

  let formattedText = text

  // 处理视频链接 - 移除视频链接，因为我们会单独显示视频
  formattedText = formattedText.replace(/<a[^>]*?href="https:\/\/video\.weibo\.com[^"]*?"[^>]*?>.*?<\/a>/g, '')

  // 修复已有的HTML标签 - 处理表情符号img标签
  formattedText = formattedText.replace(
    /<img([^>]*?)\/?\s*>/g,
    (_, attrs) => {
      // 确保img标签正确闭合
      let cleanAttrs = attrs.trim()

      // 添加weibo-emoji class
      if (cleanAttrs.includes('class=')) {
        if (!cleanAttrs.includes('weibo-emoji')) {
          cleanAttrs = cleanAttrs.replace(/class="([^"]*)"/, 'class="$1 weibo-emoji"')
        }
      } else {
        cleanAttrs += ' class="weibo-emoji"'
      }

      return `<img${cleanAttrs.startsWith(' ') ? '' : ' '}${cleanAttrs} />`
    }
  )

  // 修复@用户链接 - 处理已存在的a标签
  formattedText = formattedText.replace(
    /<a([^>]*?)href="([^"]*?)"([^>]*?)usercard="([^"]*?)"([^>]*?)>([^<]*?)<\/a>/g,
    '<span class="mention-user">$6</span>'
  )

  // 处理话题标签
  formattedText = formattedText.replace(/#([^#]+)#/g, '<span class="topic-tag">#$1#</span>')

  // 处理简单的@用户（没有链接的）
  formattedText = formattedText.replace(/@([^\s@<]+)/g, '<span class="mention-user">@$1</span>')

  // 清理多余的空白字符和特殊字符
  formattedText = formattedText.replace(/\s*​​​\s*/g, ' ')
  formattedText = formattedText.replace(/\s+/g, ' ').trim()

  return formattedText
}

// 获取微博图片
const getWeiboImages = (item) => {
  const weiboData = getWeiboData(item)
  const images = []

  // 处理 pic_infos 格式的图片（主要格式）
  if (weiboData.pic_infos && typeof weiboData.pic_infos === 'object') {
    Object.values(weiboData.pic_infos).forEach(pic => {
      if (pic && typeof pic === 'object') {
        images.push({
          thumbnail: pic.thumbnail?.url || pic.bmiddle?.url,
          large: pic.largest?.url || pic.large?.url || pic.mw2000?.url,
          original: pic.original?.url
        })
      }
    })
  }

  // 处理 pic_urls 格式的图片（备用格式）
  if (images.length === 0 && weiboData.pic_urls && Array.isArray(weiboData.pic_urls)) {
    weiboData.pic_urls.forEach(pic => {
      images.push({
        thumbnail: pic.thumbnail_pic || pic.url,
        large: pic.bmiddle_pic || pic.url,
        original: pic.original_pic || pic.url
      })
    })
  }

  // 处理单张图片（备用格式）
  if (images.length === 0 && weiboData.original_pic) {
    images.push({
      thumbnail: weiboData.thumbnail_pic || weiboData.original_pic,
      large: weiboData.bmiddle_pic || weiboData.original_pic,
      original: weiboData.original_pic
    })
  }

  console.log('微博图片数据:', weiboData.pic_infos)
  console.log('提取的图片:', images)
  return images
}

// 获取微博视频
const getWeiboVideo = (item) => {
  const weiboData = getWeiboData(item)

  // 检查是否有视频 - 根据真实数据结构
  if (weiboData.page_info && (weiboData.page_info.type === '11' || weiboData.page_info.object_type === 'video')) {
    const pageInfo = weiboData.page_info
    const mediaInfo = pageInfo.media_info

    const videoData = {
      cover: pageInfo.page_pic || pageInfo.pic_info?.pic_big?.url,
      url: mediaInfo?.stream_url || mediaInfo?.mp4_720p_mp4 || mediaInfo?.stream_url_hd,
      title: pageInfo.page_title || pageInfo.content1,
      duration: mediaInfo?.duration ? `${Math.floor(mediaInfo.duration / 60)}:${String(Math.floor(mediaInfo.duration % 60)).padStart(2, '0')}` : null,
      viewCount: mediaInfo?.online_users || pageInfo.online_users,
      type: 'video'
    }

    return videoData
  }

  return null
}

// 获取转发微博的视频
const getRetweetedVideo = (item) => {
  const weiboData = getWeiboData(item)

  // 检查是否有转发微博
  if (!weiboData.retweeted_status) {
    return null
  }

  // 根据JSON数据结构，视频信息在主微博的page_info中，但属于转发微博
  // 当存在转发微博时，主微博的page_info实际上是转发微博的视频信息
  if (weiboData.page_info && (weiboData.page_info.type === '11' || weiboData.page_info.object_type === 'video')) {
    const pageInfo = weiboData.page_info
    const mediaInfo = pageInfo.media_info

    const videoData = {
      cover: pageInfo.page_pic || pageInfo.pic_info?.pic_big?.url,
      url: mediaInfo?.stream_url || mediaInfo?.mp4_720p_mp4 || mediaInfo?.stream_url_hd,
      title: pageInfo.page_title || pageInfo.content1,
      duration: mediaInfo?.duration ? `${Math.floor(mediaInfo.duration / 60)}:${String(Math.floor(mediaInfo.duration % 60)).padStart(2, '0')}` : null,
      viewCount: mediaInfo?.online_users || pageInfo.online_users,
      type: 'video'
    }

    return videoData
  }

  return null
}

// 获取微博直播
const getWeiboLive = (item) => {
  const weiboData = getWeiboData(item)

  // 检查是否有直播 - type为26或object_type为live
  if (weiboData.page_info && (weiboData.page_info.type === '26' || weiboData.page_info.object_type === 'live')) {
    const pageInfo = weiboData.page_info
    const mediaInfo = pageInfo.media_info

    // 检查是否有live数据（直播已结束的情况）
    let playUrl = mediaInfo?.live_ld || mediaInfo?.stream_url
    let isLiveEnded = false

    if (item.live && item.live.data && item.live.data.item) {
      const liveItem = item.live.data.item
      // status: 3 表示直播已结束
      if (liveItem.status === 3 && liveItem.stream_info && liveItem.stream_info.replay_url) {
        playUrl = liveItem.stream_info.replay_url.origin
        isLiveEnded = true
      }
    }

    const liveData = {
      cover: pageInfo.page_pic || pageInfo.pic_info?.pic_big?.url,
      url: playUrl,
      title: pageInfo.page_title || pageInfo.content1,
      isLive: !isLiveEnded && mediaInfo?.live_status === 1,
      isEnded: isLiveEnded,
      viewCount: mediaInfo?.real_chatroom_users,
      type: 'live'
    }

    return liveData
  }

  return null
}



// 格式化数字
const formatCount = (count) => {
  if (!count || count === 0) return '0'

  const num = parseInt(count)
  if (num < 1000) return num.toString()
  if (num < 10000) return (num / 1000).toFixed(1) + 'K'
  if (num < 100000) return Math.floor(num / 10000) + '万'

  return (num / 10000).toFixed(1) + '万'
}

// 视频播放器状态
const videoPlayerVisible = ref(false)
const currentVideoUrl = ref('')
const isLiveStream = ref(false)
const isReplayMode = ref(false)
let hls = null

// 获取播放器标题
const getPlayerTitle = () => {
  if (isReplayMode.value) {
    return '直播回放'
  } else if (isLiveStream.value) {
    return '直播播放'
  } else {
    return '视频播放'
  }
}

// 初始化HLS播放器
const initHlsPlayer = async (url) => {
  await nextTick()

  if (!videoElement.value) return

  // 清理之前的HLS实例
  if (hls) {
    hls.destroy()
    hls = null
  }

  if (Hls.isSupported()) {
    hls = new Hls({
      enableWorker: false,
      lowLatencyMode: true,
      backBufferLength: 90
    })

    hls.loadSource(url)
    hls.attachMedia(videoElement.value)

    hls.on(Hls.Events.MANIFEST_PARSED, () => {
      console.log('HLS manifest parsed, starting playback')
      videoElement.value.play().catch(e => {
        console.log('Auto-play failed:', e)
      })
    })

    hls.on(Hls.Events.ERROR, (_, data) => {
      console.error('HLS error:', data)
      if (data.fatal) {
        ElMessage.error('直播加载失败，请稍后重试')
      }
    })
  } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
    // Safari原生支持HLS
    videoElement.value.src = url
    videoElement.value.play().catch(e => {
      console.log('Auto-play failed:', e)
    })
  } else {
    ElMessage.error('您的浏览器不支持直播播放')
  }
}

// 播放视频
const playVideo = (url) => {
  if (url) {
    currentVideoUrl.value = url
    isLiveStream.value = false
    isReplayMode.value = false
    videoPlayerVisible.value = true

    // 普通视频直接设置src
    nextTick(() => {
      if (videoElement.value) {
        videoElement.value.src = url
      }
    })
  }
}

// 关闭视频播放器
const closeVideoPlayer = () => {
  // 清理HLS实例
  if (hls) {
    hls.destroy()
    hls = null
  }

  videoPlayerVisible.value = false
  currentVideoUrl.value = ''
  isLiveStream.value = false
  isReplayMode.value = false
}

// 打开直播
const openLive = (url, isReplay = false) => {
  if (url) {
    // 使用HLS播放器在当前窗口播放直播/回放
    currentVideoUrl.value = url
    isLiveStream.value = !isReplay // 回放时不是直播流
    isReplayMode.value = isReplay
    videoPlayerVisible.value = true

    // 初始化HLS播放器
    initHlsPlayer(url)
  }
}

// ============== 长文本展开功能 ===============

// 判断是否应该显示展开按钮
const shouldShowExpandButton = (item) => {
  const weiboData = getWeiboData(item)
  return weiboData.isLongText === true
}

// 获取显示的文本内容
const getDisplayText = (item) => {
  const weiboData = getWeiboData(item)

  // 如果已展开且有长文本，显示长文本
  if (item.expanded && item.long_text) {
    return formatWeiboText(item.long_text)
  }

  // 否则显示原始文本
  return formatWeiboText(weiboData.text || weiboData.text_raw)
}

// 切换文本展开状态
const toggleExpandText = async (item) => {
  const weiboData = getWeiboData(item)

  // 如果已经展开，则收起
  if (item.expanded) {
    item.expanded = false
    return
  }

  // 如果已有长文本，直接展开
  if (item.long_text) {
    item.expanded = true
    return
  }

  // 否则请求长文本
  try {
    item.expandLoading = true
    const res = await fetchLongText({ mid: weiboData.mid || weiboData.id })

    if (res.code === 0 && res.data) {
      item.long_text = res.data.longTextContent || res.data.text || res.data
      item.expanded = true
    } else {
      // 如果是正在获取中的提示，显示友好信息
      if (res.msg && res.msg.includes('正在获取中')) {
        ElMessage.info('正在获取完整内容，请稍后重试')
      } else {
        ElMessage.error('获取完整内容失败')
      }
    }
  } catch (error) {
    console.error('获取长文本失败:', error)
    ElMessage.error('获取完整内容失败，请稍后重试')
  } finally {
    item.expandLoading = false
  }
}

// 转发微博展开功能
const shouldShowRetweetedExpandButton = (item) => {
  const weiboData = getWeiboData(item)
  return weiboData.retweeted_status && weiboData.retweeted_status.isLongText === true
}

const getRetweetedDisplayText = (item) => {
  const weiboData = getWeiboData(item)
  const retweetedStatus = weiboData.retweeted_status

  if (!retweetedStatus) return ''

  // 如果已展开且有长文本，显示长文本
  if (item.retweetedExpanded && item.retweeted_long_text) {
    return formatWeiboText(item.retweeted_long_text)
  }

  // 否则显示原始文本
  return formatWeiboText(retweetedStatus.text || retweetedStatus.text_raw)
}

const toggleRetweetedExpandText = async (item) => {
  const weiboData = getWeiboData(item)
  const retweetedStatus = weiboData.retweeted_status

  if (!retweetedStatus) return

  // 如果已经展开，则收起
  if (item.retweetedExpanded) {
    item.retweetedExpanded = false
    return
  }

  // 如果已有长文本，直接展开
  if (item.retweeted_long_text) {
    item.retweetedExpanded = true
    return
  }

  // 否则请求长文本
  try {
    item.retweetedExpandLoading = true
    const res = await fetchLongText({ mid: retweetedStatus.mid || retweetedStatus.id })

    if (res.code === 0 && res.data) {
      item.retweeted_long_text = res.data.longTextContent || res.data.text || res.data
      item.retweetedExpanded = true
    } else {
      // 如果是正在获取中的提示，显示友好信息
      if (res.msg && res.msg.includes('正在获取中')) {
        ElMessage.info('正在获取完整内容，请稍后重试')
      } else {
        ElMessage.error('获取完整内容失败')
      }
    }
  } catch (error) {
    console.error('获取转发微博长文本失败:', error)
    ElMessage.error('获取完整内容失败，请稍后重试')
  } finally {
    item.retweetedExpandLoading = false
  }
}

// 初始化时间线数据时，为每个item添加展开相关的状态
const initializeItemStates = (items) => {
  return items.map(item => ({
    ...item,
    expanded: false,
    expandLoading: false,
    long_text: null,
    retweetedExpanded: false,
    retweetedExpandLoading: false,
    retweeted_long_text: null
  }))
}

// 初始化加载数据
getTimelineData()

// ============== 时间线控制部分结束 ===============

// 删除行
const deleteRow = (row) => {
    ElMessageBox.confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
            deleteWeiboBlogFunc(row)
        })
    }

// 删除行
const deleteWeiboBlogFunc = async (row) => {
    const res = await deleteWeiboBlog({ ID: row.ID })
    if (res.code === 0) {
        ElMessage({
                type: 'success',
                message: '删除成功'
            })
        // 从本地数组中移除该项，保持在原地
        const index = timelineData.value.findIndex(item => item.ID === row.ID)
        if (index !== -1) {
            timelineData.value.splice(index, 1)
        }
    }
}

const detailForm = ref({})

// 查看详情控制标记
const detailShow = ref(false)


// 打开详情弹窗
const openDetailShow = () => {
  detailShow.value = true
}


// 打开详情
const getDetails = async (row) => {
  // 打开弹窗
  const res = await findWeiboBlog({ ID: row.ID })
  if (res.code === 0) {
    detailForm.value = res.data
    openDetailShow()
  }
}


// 关闭详情弹窗
const closeDetailShow = () => {
  detailShow.value = false
  detailForm.value = {}
}

// ============== 抓取功能相关 ===============
// 抓取对话框控制
const crawlDialogVisible = ref(false)
const crawlLoading = ref(false)
const crawlFormRef = ref()

// 抓取表单数据
const crawlForm = ref({
  uid: '',
  startPage: 1,
  maxPages: 100,
  sinceId: '',
  cookies: ''
})

// 抓取表单验证规则
const crawlRules = {
  uid: [
    { required: true, message: '请输入微博用户ID', trigger: 'blur' },
    { pattern: /^\d+$/, message: '用户ID必须是数字', trigger: 'blur' }
  ],
  startPage: [
    { type: 'number', min: 1, message: '起始页码必须大于0', trigger: 'blur' }
  ],
  maxPages: [
    { type: 'number', min: 1, message: '最大页数必须大于0', trigger: 'blur' }
  ]
}

// 打开抓取对话框
const openCrawlDialog = () => {
  crawlDialogVisible.value = true
}

// 关闭抓取对话框
const closeCrawlDialog = () => {
  crawlDialogVisible.value = false
  crawlForm.value = {
    uid: '',
    startPage: 1,
    maxPages: 100,
    sinceId: '',
    cookies: ''
  }
  crawlFormRef.value?.resetFields()
}

// 开始抓取
const startCrawl = async () => {
  if (!crawlFormRef.value) return

  try {
    const valid = await crawlFormRef.value.validate()
    if (!valid) return

    crawlLoading.value = true

    const params = {
      uid: crawlForm.value.uid,
      startPage: crawlForm.value.startPage,
      maxPages: crawlForm.value.maxPages
    }

    // 只有非空值才添加到参数中
    if (crawlForm.value.sinceId) {
      params.sinceId = crawlForm.value.sinceId
    }
    if (crawlForm.value.cookies) {
      params.cookies = crawlForm.value.cookies
    }

    const res = await crawlWeiboData(params)

    if (res.code === 0) {
      ElMessage.success('抓取任务已启动，请稍后查看数据')
      closeCrawlDialog()

      // 延迟3秒后刷新数据，给后端一些处理时间
      setTimeout(() => {
        resetTimeline()
      }, 3000)
    } else {
      ElMessage.error(res.msg || '抓取失败')
    }
  } catch (error) {
    console.error('抓取失败:', error)
    ElMessage.error('抓取失败，请检查网络连接')
  } finally {
    crawlLoading.value = false
  }
}


</script>

<style scoped>
.gva-timeline-box {
  margin-top: 20px;
}

.timeline-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.timeline-item-header h4 {
  margin: 0;
  color: #409eff;
  font-weight: 600;
}

.timeline-item-actions {
  display: flex;
  gap: 8px;
}

.timeline-item-content {
  margin: 8px 0;
  color: #606266;
  line-height: 1.5;
}

.timeline-item-data {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.timeline-label {
  font-weight: 500;
  color: #303133;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  color: #909399;
  gap: 8px;
}

.no-more-data {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
}

.el-timeline-item__card {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.el-timeline-item__card:hover {
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 抓取对话框样式 */
.form-tip {
  margin-top: 4px;
}

.form-tip .el-text {
  font-size: 12px;
  line-height: 1.4;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.el-dialog .el-form-item {
  margin-bottom: 20px;
}

.el-dialog .el-input-number {
  width: 100%;
}

.el-dialog .el-textarea {
  width: 100%;
}

/* 微博卡片样式 */
.weibo-card {
  margin-bottom: 16px;
  border-radius: 12px;
  overflow: hidden;
}

.weibo-card :deep(.el-card__body) {
  padding: 16px;
}

/* 微博头部 */
.weibo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.user-info {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  flex: 1;
}

.user-name {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  color: #333;
  font-size: 15px;
  margin-bottom: 4px;
}

.verified-tag {
  margin-left: 4px;
}

.publish-info {
  font-size: 13px;
  color: #8590a6;
}

.source {
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 微博内容 */
.weibo-content {
  margin-bottom: 12px;
}

.weibo-text {
  font-size: 15px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 12px;
  word-wrap: break-word;
}

.weibo-text :deep(.topic-tag) {
  color: #1890ff;
  font-weight: 500;
}

.weibo-text :deep(.mention-user) {
  color: #1890ff;
}

.weibo-text :deep(.weibo-link) {
  color: #1890ff;
  text-decoration: none;
}

.weibo-text :deep(.weibo-link:hover) {
  text-decoration: underline;
}

.weibo-text :deep(.weibo-emoji) {
  width: 20px !important;
  height: 20px !important;
  vertical-align: middle !important;
  margin: 0 2px !important;
  display: inline-block !important;
  border: none !important;
  background: transparent !important;
  max-width: none !important;
  max-height: none !important;
}

/* 确保表情符号正确显示 */
.weibo-text :deep(img.weibo-emoji) {
  width: 20px !important;
  height: 20px !important;
  vertical-align: middle !important;
  margin: 0 2px !important;
  display: inline-block !important;
  border: none !important;
  background: transparent !important;
}

/* 展开按钮样式 */
.expand-button-container {
  margin-top: 8px;
  text-align: left;
}

.expand-button-container .el-button {
  padding: 0;
  font-size: 14px;
  color: #1890ff;
}

.expand-button-container .el-button:hover {
  color: #40a9ff;
}

/* 视频样式 */
.weibo-video {
  margin-top: 12px;
  max-width: 150px; /* 减小最大宽度以适应竖屏比例 */
}

.video-container {
  position: relative;
  width: 100%;
  aspect-ratio: 9/16; /* 竖屏比例 */
  max-height: 500px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-cover {
  width: 100%;
  height: 100%;
  object-fit: cover; /* 填满容器，可能会裁剪部分内容 */
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: background-color 0.3s;
}

.video-container:hover .video-overlay {
  background: rgba(0, 0, 0, 0.3);
}

.play-icon {
  color: white;
  font-size: 60px;
  opacity: 0.9;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.video-duration {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

.video-views {
  position: absolute;
  bottom: 12px;
  left: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 500;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #000;
  color: #999;
  gap: 8px;
}

/* 转发微博样式 */
.retweeted-weibo {
  margin-top: 12px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 3px solid #1890ff;
}

.retweeted-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.retweeted-username {
  font-weight: 500;
  color: #1890ff;
  font-size: 14px;
}

.retweeted-content {
  margin-left: 32px;
}

.retweeted-text {
  color: #333;
  line-height: 1.6;
  margin-bottom: 8px;
}

.retweeted-text :deep(.weibo-emoji) {
  width: 20px !important;
  height: 20px !important;
  vertical-align: middle !important;
  margin: 0 2px !important;
  display: inline-block !important;
  object-fit: contain !important;
  border: none !important;
  background: none !important;
  max-width: none !important;
  max-height: none !important;
}

/* 确保转发微博表情符号正确显示 */
.retweeted-text :deep(img.weibo-emoji) {
  width: 20px !important;
  height: 20px !important;
  vertical-align: middle !important;
  margin: 0 2px !important;
  display: inline-block !important;
}

/* 图片网格 */
.weibo-images {
  display: grid;
  gap: 8px;
  margin-top: 12px;
  grid-template-columns: repeat(3, 1fr);
  max-width: 400px;
}

.weibo-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
}

.weibo-image :deep(.el-image__inner) {
  transition: transform 0.2s;
}

.weibo-image:hover :deep(.el-image__inner) {
  transform: scale(1.05);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f5f5;
  color: #999;
  gap: 4px;
  font-size: 12px;
}

/* 互动数据 */
.weibo-stats {
  display: flex;
  gap: 24px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 12px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8590a6;
  font-size: 13px;
  cursor: pointer;
  transition: color 0.2s;
}

.stat-item:hover {
  color: #1890ff;
}

.stat-item .el-icon {
  font-size: 16px;
}

/* 直播样式 */
.weibo-live {
  margin-top: 12px;
  max-width: 400px;
}

.live-container {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.live-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.live-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.1) 50%, rgba(0,0,0,0.6) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.live-status {
  position: absolute;
  top: 12px;
  left: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  background: rgba(0, 0, 0, 0.6);
  color: white;
}

.live-status.live-active {
  background: #ff4757;
  color: white;
  animation: pulse 2s infinite;
}

.live-status.live-ended {
  background: #2ed573;
  color: white;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.live-viewers {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.live-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f5f5;
  color: #999;
}

.live-placeholder .el-icon {
  font-size: 40px;
  margin-bottom: 8px;
}

/* 视频播放器弹窗样式 */
.video-player-dialog {
  max-width: 90vw;
}

.video-player-dialog :deep(.el-dialog__body) {
  padding: 0;
}

.video-player-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  min-height: 60vh;
}

.video-player {
  width: 100%;
  height: auto;
  max-height: 80vh;
  object-fit: contain;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .video-player-dialog {
    width: 95% !important;
  }

  .video-player-container {
    min-height: 50vh;
  }

  .video-player {
    max-height: 70vh;
  }
}
</style>
