#!/usr/bin/env node

/**
 * 调试脚本 - 用于诊断 VSCode 调试环境和命令行环境的差异
 */

const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')

console.log('🔍 开始诊断环境差异...\n')

// 1. 检查 Node.js 版本
console.log('📋 环境信息:')
console.log(`  Node.js 版本: ${process.version}`)
console.log(`  平台: ${process.platform}`)
console.log(`  架构: ${process.arch}`)
console.log(`  当前工作目录: ${process.cwd()}`)
console.log(`  脚本路径: ${__filename}`)
console.log(`  脚本目录: ${__dirname}`)

// 2. 检查环境变量
console.log('\n🌍 关键环境变量:')
const importantEnvs = ['NODE_ENV', 'PATH', 'npm_config_user_config', 'npm_config_cache']
importantEnvs.forEach(env => {
  console.log(`  ${env}: ${process.env[env] || '未设置'}`)
})

// 3. 检查文件存在性
console.log('\n📁 文件检查:')
const filesToCheck = [
  '.env.development',
  '.env.production',
  'package.json',
  'vite.config.js',
  'node_modules'
]

filesToCheck.forEach(file => {
  const exists = fs.existsSync(path.join(__dirname, file))
  console.log(`  ${file}: ${exists ? '✅ 存在' : '❌ 不存在'}`)
})

// 4. 检查 .env.development 内容
console.log('\n⚙️  .env.development 内容:')
try {
  const envPath = path.join(__dirname, '.env.development')
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8')
    console.log(envContent)
  } else {
    console.log('  ❌ .env.development 文件不存在')
  }
} catch (error) {
  console.log(`  ❌ 读取 .env.development 失败: ${error.message}`)
}

// 5. 检查 npm 配置
console.log('\n📦 npm 信息:')
try {
  const npmVersion = require('child_process').execSync('npm --version', { encoding: 'utf8' }).trim()
  console.log(`  npm 版本: ${npmVersion}`)
} catch (error) {
  console.log(`  ❌ 获取 npm 版本失败: ${error.message}`)
}

// 6. 启动开发服务器
console.log('\n🚀 启动开发服务器...')
console.log('如果出现错误，请查看上面的诊断信息\n')

// 使用 spawn 启动 vite，这样可以看到实时输出
const child = spawn('npm', ['run', 'serve'], {
  stdio: 'inherit',
  shell: true,
  cwd: __dirname
})

child.on('error', (error) => {
  console.error(`❌ 启动失败: ${error.message}`)
})

child.on('exit', (code) => {
  console.log(`\n🏁 进程退出，退出码: ${code}`)
})

// 处理进程终止信号
process.on('SIGINT', () => {
  console.log('\n⏹️  收到终止信号，正在关闭...')
  child.kill('SIGINT')
})

process.on('SIGTERM', () => {
  console.log('\n⏹️  收到终止信号，正在关闭...')
  child.kill('SIGTERM')
})
