aliyun-oss:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    bucket-url: yourBucketUrl
    base-path: yourBasePath
autocode:
    web: web/src
    root: D:\wwwroot\gin-vue-admin
    server: server
    module: github.com/flipped-aurora/gin-vue-admin/server
    ai-path: ""
aws-s3:
    bucket: xxxxx-********
    region: ap-shanghai
    endpoint: ""
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
    s3-force-path-style: false
    disable-ssl: false
captcha:
    key-long: 6
    img-width: 240
    img-height: 80
    open-captcha: 0
    open-captcha-timeout: 3600
cloudflare-r2:
    bucket: xxxx0bucket
    base-url: https://gin.vue.admin.com
    path: uploads
    account-id: xxx_account_id
    access-key-id: xxx_key_id
    secret-access-key: xxx_secret_key
cors:
    mode: strict-whitelist
    whitelist:
        - allow-origin: example1.com
          allow-methods: POST, GET
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
        - allow-origin: example2.com
          allow-methods: GET, POST
          allow-headers: content-type
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
db-list:
    - type: ""
      alias-name: ""
      prefix: ""
      port: ""
      config: ""
      db-name: ""
      username: ""
      password: ""
      path: ""
      engine: ""
      log-mode: ""
      max-idle-conns: 10
      max-open-conns: 100
      singular: false
      log-zap: false
      disable: true
disk-list:
    - mount-point: /
email:
    to: <EMAIL>
    from: <EMAIL>
    host: smtp.163.com
    secret: xxx
    nickname: test
    port: 465
    is-ssl: true
    is-loginauth: false
excel:
    dir: ./resource/excel/
hua-wei-obs:
    path: you-path
    bucket: you-bucket
    endpoint: you-endpoint
    access-key: you-access-key
    secret-key: you-secret-key
jwt:
    signing-key: 008a5ff8-0b48-4710-87db-1739b3ca40c1
    expires-time: 7d
    buffer-time: 1d
    issuer: qmPlus
local:
    path: uploads/file
    store-path: uploads/file
mcp:
    name: GVA_MCP
    version: v1.0.0
    sse_path: /sse
    message_path: /message
    url_prefix: ""
minio:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    use-ssl: false
    base-path: ""
    bucket-url: http://host:9000/yourBucketName
mongo:
    coll: ""
    options: ""
    database: ""
    username: ""
    password: ""
    auth-source: ""
    min-pool-size: 0
    max-pool-size: 100
    socket-timeout-ms: 0
    connect-timeout-ms: 0
    is-zap: false
    hosts:
        - host: ""
          port: ""
mssql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
mysql:
    prefix: ""
    port: "3306"
    config: charset=utf8mb4&parseTime=True&loc=Local
    db-name: gva
    username: root
    password: "123456"
    path: 127.0.0.1
    engine: ""
    log-mode: error
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
oracle:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
pgsql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
qiniu:
    zone: ZoneHuaDong
    bucket: ""
    img-path: ""
    access-key: ""
    secret-key: ""
    use-https: false
    use-cdn-domains: false
redis:
    name: ""
    addr: 127.0.0.1:6379
    password: ""
    db: 0
    useCluster: false
    clusterAddrs:
        - **********:7000
        - **********:7001
        - **********:7002
redis-list:
    - name: cache
      addr: 127.0.0.1:6379
      password: ""
      db: 0
      useCluster: false
      clusterAddrs:
        - **********:7000
        - **********:7001
        - **********:7002
sqlite:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
system:
    db-type: mysql
    oss-type: local
    router-prefix: ""
    addr: 8688
    iplimit-count: 15000
    iplimit-time: 3600
    use-multipoint: false
    use-redis: false
    use-mongo: false
    use-strict-auth: false
tencent-cos:
    bucket: xxxxx-********
    region: ap-shanghai
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
zap:
    level: info
    prefix: '[github.com/flipped-aurora/gin-vue-admin/server]'
    format: console
    director: log
    encode-level: LowercaseColorLevelEncoder
    stacktrace-key: stacktrace
    show-line: true
    log-in-console: true
    retention-day: -1
