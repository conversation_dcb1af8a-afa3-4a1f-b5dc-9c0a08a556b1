import service from '@/utils/request'
// @Tags WeiboBlog
// @Summary 创建微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.WeiboBlog true "创建微博"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"创建成功"}"
// @Router /weiboBlog/createWeiboBlog [post]
export const createWeiboBlog = (data) => {
  return service({
    url: '/weiboBlog/createWeiboBlog',
    method: 'post',
    data
  })
}

// @Tags WeiboBlog
// @Summary 删除微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.WeiboBlog true "删除微博"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /weiboBlog/deleteWeiboBlog [delete]
export const deleteWeiboBlog = (params) => {
  return service({
    url: '/weiboBlog/deleteWeiboBlog',
    method: 'delete',
    params
  })
}

// @Tags WeiboBlog
// @Summary 批量删除微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除微博"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /weiboBlog/deleteWeiboBlog [delete]
export const deleteWeiboBlogByIds = (params) => {
  return service({
    url: '/weiboBlog/deleteWeiboBlogByIds',
    method: 'delete',
    params
  })
}

// @Tags WeiboBlog
// @Summary 更新微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.WeiboBlog true "更新微博"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /weiboBlog/updateWeiboBlog [put]
export const updateWeiboBlog = (data) => {
  return service({
    url: '/weiboBlog/updateWeiboBlog',
    method: 'put',
    data
  })
}

// @Tags WeiboBlog
// @Summary 用id查询微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query model.WeiboBlog true "用id查询微博"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /weiboBlog/findWeiboBlog [get]
export const findWeiboBlog = (params) => {
  return service({
    url: '/weiboBlog/findWeiboBlog',
    method: 'get',
    params
  })
}

// @Tags WeiboBlog
// @Summary 分页获取微博列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取微博列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /weiboBlog/getWeiboBlogList [get]
export const getWeiboBlogList = (params) => {
  return service({
    url: '/weiboBlog/getWeiboBlogList',
    method: 'get',
    params
  })
}

// @Tags WeiboBlog
// @Summary 不需要鉴权的微博接口
// @Accept application/json
// @Produce application/json
// @Param data query weiboReq.WeiboBlogSearch true "分页获取微博列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /weiboBlog/getWeiboBlogPublic [get]
export const getWeiboBlogPublic = () => {
  return service({
    url: '/weiboBlog/getWeiboBlogPublic',
    method: 'get',
  })
}

// @Tags WeiboBlog
// @Summary 抓取微博数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param uid query string true "微博用户ID"
// @Param startPage query int false "起始页码"
// @Param maxPages query int false "最大抓取页数"
// @Param sinceId query string false "起始since_id"
// @Param cookies query string false "微博cookies"
// @Success 200 {object} response.Response{data=object,msg=string} "抓取成功"
// @Router /weiboBlog/crawlWeiboData [post]
export const crawlWeiboData = (params) => {
  return service({
    url: '/weiboBlog/crawlWeiboData',
    method: 'post',
    params
  })
}

// @Tags WeiboBlog
// @Summary 获取微博长文本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param mid query string true "微博ID"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /weiboBlog/fetchLongText [get]
export const fetchLongText = (params) => {
  return service({
    url: '/weiboBlog/fetchLongText',
    method: 'get',
    params
  })
}
