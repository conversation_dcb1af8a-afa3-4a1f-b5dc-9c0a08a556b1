package weibo

import (
	"compress/gzip"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/weibo"
	weiboReq "github.com/flipped-aurora/gin-vue-admin/server/model/weibo/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/datatypes"
)

type WeiboBlogApi struct{}

// LiveStreamMonitor 直播流监控管理器
type LiveStreamMonitor struct {
	mu            sync.RWMutex
	monitoringMap map[string]bool // roomID -> isMonitoring
}

// 全局直播监控管理器
var liveMonitor = &LiveStreamMonitor{
	monitoringMap: make(map[string]bool),
}

// isMonitoring 检查是否正在监控
func (lsm *LiveStreamMonitor) isMonitoring(roomID string) bool {
	lsm.mu.RLock()
	defer lsm.mu.RUnlock()
	return lsm.monitoringMap[roomID]
}

// startMonitoring 开始监控
func (lsm *LiveStreamMonitor) startMonitoring(roomID string) bool {
	lsm.mu.Lock()
	defer lsm.mu.Unlock()

	if lsm.monitoringMap[roomID] {
		return false // 已在监控中
	}

	lsm.monitoringMap[roomID] = true
	return true // 成功开始监控
}

// stopMonitoring 停止监控
func (lsm *LiveStreamMonitor) stopMonitoring(roomID string) {
	lsm.mu.Lock()
	defer lsm.mu.Unlock()
	delete(lsm.monitoringMap, roomID)
}

// getMonitoringCount 获取正在监控的直播流数量
func (lsm *LiveStreamMonitor) getMonitoringCount() int {
	lsm.mu.RLock()
	defer lsm.mu.RUnlock()
	return len(lsm.monitoringMap)
}

// getMonitoringList 获取正在监控的直播流列表
func (lsm *LiveStreamMonitor) getMonitoringList() []string {
	lsm.mu.RLock()
	defer lsm.mu.RUnlock()

	var list []string
	for roomID := range lsm.monitoringMap {
		list = append(list, roomID)
	}
	return list
}

// CreateWeiboBlog 创建微博
// @Tags WeiboBlog
// @Summary 创建微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body weibo.WeiboBlog true "创建微博"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /weiboBlog/createWeiboBlog [post]
func (weiboBlogApi *WeiboBlogApi) CreateWeiboBlog(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var weiboBlog weibo.WeiboBlog
	err := c.ShouldBindJSON(&weiboBlog)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = weiboBlogService.CreateWeiboBlog(ctx, &weiboBlog)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteWeiboBlog 删除微博
// @Tags WeiboBlog
// @Summary 删除微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body weibo.WeiboBlog true "删除微博"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /weiboBlog/deleteWeiboBlog [delete]
func (weiboBlogApi *WeiboBlogApi) DeleteWeiboBlog(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := weiboBlogService.DeleteWeiboBlog(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteWeiboBlogByIds 批量删除微博
// @Tags WeiboBlog
// @Summary 批量删除微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /weiboBlog/deleteWeiboBlogByIds [delete]
func (weiboBlogApi *WeiboBlogApi) DeleteWeiboBlogByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := weiboBlogService.DeleteWeiboBlogByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateWeiboBlog 更新微博
// @Tags WeiboBlog
// @Summary 更新微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body weibo.WeiboBlog true "更新微博"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /weiboBlog/updateWeiboBlog [put]
func (weiboBlogApi *WeiboBlogApi) UpdateWeiboBlog(c *gin.Context) {
	// 从ctx获取标准context进行业务行为
	ctx := c.Request.Context()

	var weiboBlog weibo.WeiboBlog
	err := c.ShouldBindJSON(&weiboBlog)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = weiboBlogService.UpdateWeiboBlog(ctx, weiboBlog)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindWeiboBlog 用id查询微博
// @Tags WeiboBlog
// @Summary 用id查询微博
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询微博"
// @Success 200 {object} response.Response{data=weibo.WeiboBlog,msg=string} "查询成功"
// @Router /weiboBlog/findWeiboBlog [get]
func (weiboBlogApi *WeiboBlogApi) FindWeiboBlog(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reweiboBlog, err := weiboBlogService.GetWeiboBlog(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reweiboBlog, c)
}

// GetWeiboBlogList 分页获取微博列表
// @Tags WeiboBlog
// @Summary 分页获取微博列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query weiboReq.WeiboBlogSearch true "分页获取微博列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /weiboBlog/getWeiboBlogList [get]
func (weiboBlogApi *WeiboBlogApi) GetWeiboBlogList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo weiboReq.WeiboBlogSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := weiboBlogService.GetWeiboBlogInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetWeiboBlogPublic 不需要鉴权的微博接口
// @Tags WeiboBlog
// @Summary 不需要鉴权的微博接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /weiboBlog/getWeiboBlogPublic [get]
func (weiboBlogApi *WeiboBlogApi) GetWeiboBlogPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权
	// 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	weiboBlogService.GetWeiboBlogPublic(ctx)
	response.OkWithDetailed(gin.H{
		"info": "不需要鉴权的微博接口信息",
	}, "获取成功", c)
}

// CrawlWeiboData 抓取微博数据
// @Tags WeiboBlog
// @Summary 抓取微博数据
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param uid query string true "微博用户ID"
// @Param startPage query int false "起始页码" default(1)
// @Param maxPages query int false "最大抓取页数" default(100)
// @Param sinceId query string false "起始since_id"
// @Param cookies query string false "微博cookies"
// @Success 200 {object} response.Response{data=object,msg=string} "抓取成功"
// @Router /weiboBlog/crawlWeiboData [post]
func (weiboBlogApi *WeiboBlogApi) CrawlWeiboData(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取参数
	uid := c.Query("uid")
	if uid == "" {
		response.FailWithMessage("用户ID不能为空", c)
		return
	}

	startPageStr := c.DefaultQuery("startPage", "1")
	maxPagesStr := c.DefaultQuery("maxPages", "100")
	sinceId := c.Query("sinceId")
	cookies := c.Query("cookies")

	startPage, err := strconv.Atoi(startPageStr)
	if err != nil {
		response.FailWithMessage("起始页码格式错误", c)
		return
	}

	maxPages, err := strconv.Atoi(maxPagesStr)
	if err != nil {
		response.FailWithMessage("最大页数格式错误", c)
		return
	}

	// 开始抓取
	go weiboBlogApi.crawlWeiboDataAsync(ctx, uid, startPage, maxPages, sinceId, cookies)

	response.OkWithMessage("抓取任务已启动", c)
}

// FetchLongText 获取微博长文本
// @Tags WeiboBlog
// @Summary 获取微博长文本
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param mid query string true "微博ID"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /weiboBlog/fetchLongText [get]
func (weiboBlogApi *WeiboBlogApi) FetchLongText(c *gin.Context) {
	ctx := c.Request.Context()

	// 获取参数
	mid := c.Query("mid")
	if mid == "" {
		response.FailWithMessage("微博ID不能为空", c)
		return
	}

	// 从数据库查找对应的微博记录
	var weiboBlog weibo.WeiboBlog
	err := global.GVA_DB.Where("blog_id = ?", mid).First(&weiboBlog).Error
	if err != nil {
		global.GVA_LOG.Error("查询微博失败!", zap.Error(err), zap.String("mid", mid))
		response.FailWithMessage("微博不存在", c)
		return
	}

	// 检查是否已有长文本数据
	if len(weiboBlog.Long_text) > 0 {
		// 解析已有的长文本数据
		var longTextData map[string]interface{}
		err := json.Unmarshal(weiboBlog.Long_text, &longTextData)
		if err == nil {
			// 提取长文本内容
			if data, ok := longTextData["data"].(map[string]interface{}); ok {
				if longTextContent, exists := data["longTextContent"]; exists {
					response.OkWithData(map[string]interface{}{
						"longTextContent": longTextContent,
					}, c)
					return
				}
			}
		}
	}

	// 如果没有长文本数据，尝试从原始数据中获取mblogid
	var mblogID string
	if weiboBlog.Data != nil {
		var dataMap map[string]interface{}
		if err := json.Unmarshal(weiboBlog.Data, &dataMap); err == nil {
			if mblogid, exists := dataMap["mblogid"]; exists {
				mblogID = fmt.Sprintf("%v", mblogid)
			}
		}
	}

	// 如果没有mblogID，使用mid作为备用
	if mblogID == "" {
		mblogID = mid
	}

	// 同步获取长文本
	cookies := c.DefaultQuery("cookies", "")
	weiboBlogApi.fetchLongText(ctx, weiboBlog.Blog_id, mblogID, cookies)

	// 重新查询数据库获取更新后的长文本数据
	err = global.GVA_DB.Where("blog_id = ?", mid).First(&weiboBlog).Error
	if err != nil {
		global.GVA_LOG.Error("重新查询微博失败!", zap.Error(err), zap.String("mid", mid))
		response.FailWithMessage("获取长文本失败", c)
		return
	}

	// 检查是否成功获取到长文本数据
	if len(weiboBlog.Long_text) > 0 {
		// 解析长文本数据
		var longTextData map[string]interface{}
		err := json.Unmarshal(weiboBlog.Long_text, &longTextData)
		if err == nil {
			// 提取长文本内容
			if data, ok := longTextData["data"].(map[string]interface{}); ok {
				if longTextContent, exists := data["longTextContent"]; exists {
					response.OkWithData(map[string]interface{}{
						"longTextContent": longTextContent,
					}, c)
					return
				}
			}
		}
	}

	// 如果仍然没有获取到长文本，返回失败信息
	response.FailWithMessage("获取长文本失败，请稍后重试", c)
}

// crawlWeiboDataAsync 异步抓取微博数据
func (weiboBlogApi *WeiboBlogApi) crawlWeiboDataAsync(ctx context.Context, uid string, startPage, maxPages int, sinceId, cookies string) {
	baseURL := fmt.Sprintf("https://weibo.com/ajax/statuses/mymblog?uid=%s&feature=0&page=", uid)
	page := startPage
	currentSinceId := sinceId
	var allTempBlogs []weibo.WeiboBlog // 在最外层定义临时存储数组

	shouldStopCrawling := false

	for page <= maxPages && !shouldStopCrawling {
		// 构建URL
		url := fmt.Sprintf("%s%d", baseURL, page)
		if currentSinceId != "" {
			url += "&since_id=" + currentSinceId
		}

		// 获取微博数据
		jsonData, err := weiboBlogApi.getWeiboStreamData(url, cookies)
		if err != nil {
			global.GVA_LOG.Error("抓取失败", zap.Error(err), zap.Int("page", page))
			break
		}

		if jsonData == nil {
			global.GVA_LOG.Error("获取数据为空", zap.Int("page", page))
			break
		}

		// 解析数据
		data, ok := jsonData["data"].(map[string]interface{})
		if !ok {
			global.GVA_LOG.Error("数据格式错误", zap.Int("page", page))
			break
		}

		// 获取since_id
		if sid, exists := data["since_id"]; exists && sid != nil {
			currentSinceId = fmt.Sprintf("%v", sid)
		}

		// 获取微博列表
		list, ok := data["list"].([]interface{})
		if !ok || len(list) == 0 {
			global.GVA_LOG.Info("无更多数据", zap.Int("page", page))
			break
		}

		// 处理每条微博数据（正序遍历）
		for i := 0; i < len(list); i++ {
			item := list[i]
			itemMap, ok := item.(map[string]interface{})
			if !ok {
				continue
			}

			// 跳过置顶微博
			if isTop, exists := itemMap["isTop"]; exists && isTop == 1 {
				continue
			}

			// 获取博客ID
			blogIDInterface, exists := itemMap["id"]
			if !exists {
				continue
			}

			// 直接从原始JSON中提取字符串形式的ID
			blogIDStr, ok := blogIDInterface.(string)
			if !ok {
				// 如果不是string类型，尝试从原始JSON数据中获取
				if rawBytes, err := json.Marshal(itemMap); err == nil {
					var rawMap map[string]json.RawMessage
					if json.Unmarshal(rawBytes, &rawMap) == nil {
						if idRaw, exists := rawMap["id"]; exists {
							// 去掉JSON字符串的引号
							blogIDStr = strings.Trim(string(idRaw), `"`)
						}
					}
				}
				// 如果还是获取不到，使用备用方案
				if blogIDStr == "" {
					blogIDStr = fmt.Sprintf("%.0f", blogIDInterface)
				}
			}
			if blogIDStr == "" {
				global.GVA_LOG.Error("博客ID为空", zap.Any("blogIDInterface", blogIDInterface))
				continue
			}

			// 处理直播微博 - 参考Python代码的live处理
			if pageInfo, exists := itemMap["page_info"]; exists {
				pageInfoMap, ok := pageInfo.(map[string]interface{})
				if ok {
					if objectType, exists := pageInfoMap["object_type"]; exists && objectType == "live" {
						if objectId, exists := pageInfoMap["object_id"]; exists {
							roomId := fmt.Sprintf("%v", objectId)
							// 检查是否已在监控中，避免重复监控
							if liveMonitor.startMonitoring(roomId) {
								go weiboBlogApi.handleLiveStream(ctx, blogIDStr, roomId)
							}
						}
					}
				}
			}

			// 解析发布时间
			var publishTime time.Time
			if createdAt, exists := itemMap["created_at"]; exists {
				publishTime, _ = weiboBlogApi.parseWeiboTime(fmt.Sprintf("%v", createdAt))
			}

			// 将数据转换为JSON
			jsonBytes, err := json.Marshal(itemMap)
			if err != nil {
				global.GVA_LOG.Error("JSON序列化失败", zap.Error(err))
				continue
			}

			// 创建微博记录
			blog := weibo.WeiboBlog{
				Blog_id:    blogIDStr,   // 使用string类型
				Publish_at: publishTime, // 使用time.Time类型
				Data:       datatypes.JSON(jsonBytes),
			}

			allTempBlogs = append(allTempBlogs, blog)
		}

		page++

		// 随机延时防止被封
		sleepTime := time.Duration(rand.Intn(2000)+1000) * time.Millisecond
		time.Sleep(sleepTime)
	}

	// 抓取结束后，批量保存所有临时存储的数据（倒序保存，确保时间顺序正确）
	if len(allTempBlogs) > 0 {
		savedCount := 0
		updatedCount := 0
		for i := len(allTempBlogs) - 1; i >= 0; i-- {
			blog := allTempBlogs[i]

			// 检查是否已存在（不包含deleted_at条件）
			var existingBlog weibo.WeiboBlog
			err := global.GVA_DB.Unscoped().Where("blog_id = ?", blog.Blog_id).First(&existingBlog).Error

			if err != nil {
				// 不存在，创建新记录
				if saveErr := weiboBlogService.CreateWeiboBlog(ctx, &blog); saveErr != nil {
					global.GVA_LOG.Error("创建微博失败", zap.Error(saveErr), zap.String("blogID", blog.Blog_id))
				} else {
					savedCount++
				}
			} else {
				shouldStopCrawling = true
				// 存在，更新记录
				blog.ID = existingBlog.ID // 保持原有ID
				if updateErr := weiboBlogService.UpdateWeiboBlog(ctx, blog); updateErr != nil {
					global.GVA_LOG.Error("更新微博失败", zap.Error(updateErr), zap.String("blogID", blog.Blog_id))
				} else {
					updatedCount++
				}
			}
		}
	}
}

// getWeiboStreamData 获取微博流数据
func (weiboBlogApi *WeiboBlogApi) getWeiboStreamData(url, cookies string) (map[string]interface{}, error) {
	// 从URL中提取uid用于动态设置referer
	uid := weiboBlogApi.extractUidFromUrl(url)

	// 从cookies中提取XSRF-TOKEN
	xsrfToken := weiboBlogApi.extractXsrfToken(cookies)

	// 轮换User-Agent以避免被识别
	userAgents := []string{
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.6167.160 Safari/537.36",
		"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.6099.199 Safari/537.36",
		"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.6261.95 Safari/537.36",
	}
	currentUserAgent := userAgents[rand.Intn(len(userAgents))]

	// 设置请求头
	extraHeaders := map[string]string{
		"authority":          "weibo.com",
		"client-version":     "v2.47.95",
		"referer":            fmt.Sprintf("https://weibo.com/u/%s", uid),
		"sec-ch-ua":          `"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"`,
		"sec-ch-ua-mobile":   "?0",
		"sec-ch-ua-platform": `"Windows"`,
		"sec-fetch-dest":     "empty",
		"sec-fetch-mode":     "cors",
		"sec-fetch-site":     "same-origin",
		"server-version":     "v2025.07.31.5",
		"x-requested-with":   "XMLHttpRequest",
	}

	// 只有当有有效的XSRF-TOKEN时才设置
	if xsrfToken != "" {
		extraHeaders["x-xsrf-token"] = xsrfToken
	}

	// 设置默认cookies
	if cookies == "" {
		cookies = "SINAGLOBAL=2214012497427.7573.1741315594816; SCF=Alw3DxACLcj2zHbXW_TrBrcIkRN0739Df2ti1acxd8Y5jgtEeXoEEdm__oNRWflRUhnJmgo31g7mBI_SY6b-HRY.; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9Wh_DS8sJDdgKQ1osv7vbl645JpX5KMhUgL.Foz4S05NSheReKM2dJLoIEXLxK-L1KzL1-2LxK-L1KzL1-2LxK-L1KzL1-2LxK-L1KzL1-2LxKnL12BLBKet; ULV=1753432666707:12:4:2:7396269646133.562.1753432666702:1753067456029; ALF=1756450751; SUB=_2A25FjbLvDeRhGeRH7FIW9C3EyjuIHXVm40onrDV8PUJbkNAbLXP6kW1NTYDNpWqTt5C7Ucy0bZZWF36jfB-CgC2-; XSRF-TOKEN=vIwgy_BBOZWCd4xC6e8mCFT8; WBPSESS=I9ynP0BIxiDdMfLOZua1EjtM3LKXfCRH-I5lLoavU1F-5M50kMNtA_YVkGI_4iQB39MPqNQqofCLhKclPwjL1pCkONSCkZ-6XkVkUOP-OcHr6FNLiBFBH_QCIy-uH8sCiphvgxRY58KqCXQqMW7OOw=="
	}

	// 使用通用HTTP请求方法
	options := HttpRequestOptions{
		URL:          url,
		Cookies:      cookies,
		Timeout:      60 * time.Second,
		MaxRetries:   5,
		RetryDelay:   time.Duration(rand.Intn(15000)+10000) * time.Millisecond,
		UserAgent:    currentUserAgent,
		ExtraHeaders: extraHeaders,
		EnableGzip:   true,
		EnableRetry:  true,
	}

	return weiboBlogApi.makeHttpRequest(options)
}

// parseWeiboTime 解析微博时间格式
func (weiboBlogApi *WeiboBlogApi) parseWeiboTime(timeStr string) (time.Time, error) {
	// 微博时间格式，支持多种变体
	layouts := []string{
		// 标准微博格式: "Sun Feb 02 16:46:37 +0800 2025"
		"Mon Jan 02 15:04:05 -0700 2006",
		"Mon Jan 2 15:04:05 -0700 2006",
		// 带时区名称的格式
		"Mon Jan 02 15:04:05 +0800 2006",
		"Mon Jan 2 15:04:05 +0800 2006",
		// ISO格式
		"2006-01-02 15:04:05",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05+08:00",
		"2006-01-02T15:04:05-07:00",
	}

	// 记录原始时间字符串用于调试
	global.GVA_LOG.Debug("解析时间", zap.String("timeStr", timeStr))

	for i, layout := range layouts {
		if t, err := time.Parse(layout, timeStr); err == nil {
			global.GVA_LOG.Debug("时间解析成功",
				zap.String("timeStr", timeStr),
				zap.String("layout", layout),
				zap.Int("layoutIndex", i),
				zap.Time("parsedTime", t))
			return t, nil
		}
	}

	// 如果都解析失败，记录错误并返回当前时间
	global.GVA_LOG.Warn("时间解析失败，使用当前时间", zap.String("timeStr", timeStr))
	return time.Now(), fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// extractUidFromUrl 从URL中提取用户ID
func (weiboBlogApi *WeiboBlogApi) extractUidFromUrl(url string) string {
	// URL格式: https://weibo.com/ajax/statuses/mymblog?uid=5515166761&page=2&feature=0
	if strings.Contains(url, "uid=") {
		parts := strings.Split(url, "uid=")
		if len(parts) > 1 {
			uidPart := strings.Split(parts[1], "&")[0]
			return uidPart
		}
	}
	return "5515166761" // 默认值
}

// extractXsrfToken 从cookies中提取XSRF-TOKEN
func (weiboBlogApi *WeiboBlogApi) extractXsrfToken(cookies string) string {
	if cookies == "" {
		return "vIwgy_BBOZWCd4xC6e8mCFT8" // 默认值
	}

	// 查找XSRF-TOKEN
	if strings.Contains(cookies, "XSRF-TOKEN=") {
		parts := strings.Split(cookies, "XSRF-TOKEN=")
		if len(parts) > 1 {
			tokenPart := strings.Split(parts[1], ";")[0]
			return strings.TrimSpace(tokenPart)
		}
	}

	return "vIwgy_BBOZWCd4xC6e8mCFT8" // 默认值
}

// HttpRequestOptions HTTP请求选项
type HttpRequestOptions struct {
	URL          string
	Cookies      string
	Timeout      time.Duration
	MaxRetries   int
	RetryDelay   time.Duration
	UserAgent    string
	ExtraHeaders map[string]string
	EnableGzip   bool
	EnableRetry  bool
}

// makeHttpRequest 通用HTTP请求方法
func (weiboBlogApi *WeiboBlogApi) makeHttpRequest(options HttpRequestOptions) (map[string]interface{}, error) {
	// 设置默认值
	if options.Timeout == 0 {
		options.Timeout = 30 * time.Second
	}
	if options.MaxRetries == 0 {
		options.MaxRetries = 1
	}
	if options.RetryDelay == 0 {
		options.RetryDelay = 2 * time.Second
	}
	if options.UserAgent == "" {
		options.UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
	}

	// 创建HTTP客户端
	transport := &http.Transport{
		MaxIdleConns:          100,
		MaxIdleConnsPerHost:   10,
		IdleConnTimeout:       90 * time.Second,
		TLSHandshakeTimeout:   30 * time.Second,
		ResponseHeaderTimeout: 30 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	client := &http.Client{
		Timeout:   options.Timeout,
		Transport: transport,
	}

	maxRetries := options.MaxRetries
	if !options.EnableRetry {
		maxRetries = 1
	}

	for retry := 0; retry < maxRetries; retry++ {
		// 创建请求上下文
		var ctx context.Context
		var cancel context.CancelFunc
		if options.EnableGzip {
			ctx, cancel = context.WithTimeout(context.Background(), options.Timeout-5*time.Second)
		} else {
			ctx, cancel = context.WithTimeout(context.Background(), options.Timeout)
		}
		defer cancel()

		req, err := http.NewRequestWithContext(ctx, "GET", options.URL, nil)
		if err != nil {
			return nil, err
		}

		// 设置基础请求头
		req.Header.Set("User-Agent", options.UserAgent)
		req.Header.Set("Accept", "application/json, text/plain, */*")
		req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6")

		if options.EnableGzip {
			req.Header.Set("Accept-Encoding", "gzip, deflate, br")
		}

		// 设置额外请求头
		for key, value := range options.ExtraHeaders {
			req.Header.Set(key, value)
		}

		// 设置cookies
		if options.Cookies != "" {
			req.Header.Set("Cookie", options.Cookies)
		}

		resp, err := client.Do(req)
		if err != nil {
			if !options.EnableRetry || retry == maxRetries-1 {
				return nil, fmt.Errorf("请求失败: %v", err)
			}
			global.GVA_LOG.Warn("HTTP请求失败，准备重试", zap.Error(err), zap.Int("retry", retry+1))
			time.Sleep(options.RetryDelay)
			continue
		}
		defer resp.Body.Close()

		// 检查HTTP状态码
		if resp.StatusCode != 200 {
			resp.Body.Close()
			if !options.EnableRetry || retry == maxRetries-1 {
				return nil, fmt.Errorf("HTTP状态码错误: %d", resp.StatusCode)
			}
			global.GVA_LOG.Warn("HTTP状态码异常，准备重试", zap.Int("statusCode", resp.StatusCode), zap.Int("retry", retry+1))
			time.Sleep(options.RetryDelay)
			continue
		}

		// 处理响应体
		var reader io.Reader = resp.Body
		if options.EnableGzip {
			contentEncoding := resp.Header.Get("Content-Encoding")
			if contentEncoding == "gzip" {
				gzipReader, err := gzip.NewReader(resp.Body)
				if err != nil {
					resp.Body.Close()
					if !options.EnableRetry || retry == maxRetries-1 {
						return nil, fmt.Errorf("gzip解压失败: %v", err)
					}
					global.GVA_LOG.Warn("gzip解压失败，准备重试", zap.Error(err), zap.Int("retry", retry+1))
					time.Sleep(options.RetryDelay)
					continue
				}
				defer gzipReader.Close()
				reader = gzipReader
			}
		}

		var result map[string]interface{}
		err = json.NewDecoder(reader).Decode(&result)
		if err != nil {
			resp.Body.Close()
			if !options.EnableRetry || retry == maxRetries-1 {
				return nil, fmt.Errorf("JSON解析失败: %v", err)
			}
			global.GVA_LOG.Warn("JSON解析失败，准备重试", zap.Error(err), zap.Int("retry", retry+1))
			time.Sleep(options.RetryDelay)
			continue
		}

		return result, nil
	}

	return nil, fmt.Errorf("请求失败，已重试%d次", maxRetries)
}

// fetchLongText 获取长文本内容 - 参考Python代码的长文本处理
func (weiboBlogApi *WeiboBlogApi) fetchLongText(_ context.Context, blogID, mblogID, cookies string) {
	url := fmt.Sprintf("https://weibo.com/ajax/statuses/longtext?id=%s", mblogID)

	global.GVA_LOG.Info("获取长文本", zap.String("blogID", blogID), zap.String("mblogID", mblogID))

	// 使用通用HTTP请求方法获取长文本数据
	options := HttpRequestOptions{
		URL:         url,
		Cookies:     cookies,
		Timeout:     30 * time.Second,
		MaxRetries:  3,
		RetryDelay:  2 * time.Second,
		EnableGzip:  true,
		EnableRetry: true,
	}

	longTextData, err := weiboBlogApi.makeHttpRequest(options)
	if err != nil {
		global.GVA_LOG.Error("获取长文本失败", zap.Error(err), zap.String("blogID", blogID))
		return
	}

	if longTextData == nil {
		global.GVA_LOG.Error("长文本数据为空", zap.String("blogID", blogID))
		return
	}

	// 将长文本数据转换为JSON
	longTextBytes, err := json.Marshal(longTextData)
	if err != nil {
		global.GVA_LOG.Error("长文本JSON序列化失败", zap.Error(err), zap.String("blogID", blogID))
		return
	}

	// 更新数据库中的long_text字段（不包含deleted_at条件）
	err = global.GVA_DB.Unscoped().Model(&weibo.WeiboBlog{}).
		Where("blog_id = ?", blogID).
		Update("long_text", datatypes.JSON(longTextBytes)).Error

	if err != nil {
		global.GVA_LOG.Error("更新长文本失败", zap.Error(err), zap.String("blogID", blogID))
	} else {
		global.GVA_LOG.Info("长文本更新成功", zap.String("blogID", blogID))
	}
}

// handleLiveStream 处理直播流 - 参考Python代码的直播处理
func (weiboBlogApi *WeiboBlogApi) handleLiveStream(_ context.Context, blogID, roomID string) {
	// 确保监控结束时清理状态
	defer liveMonitor.stopMonitoring(roomID)

	// 根据curl命令，直播API需要完整的roomID（包含1022:前缀）和traceId
	// 生成traceId（时间戳+随机数）
	traceId := fmt.Sprintf("%d%d", time.Now().UnixMilli(), rand.Intn(1000))

	url := fmt.Sprintf("https://weibo.com/l/pc/anchor/live?live_id=%s&traceId=%s", roomID, traceId)

	global.GVA_LOG.Info("开始监控直播",
		zap.String("blogID", blogID),
		zap.String("roomID", roomID),
		zap.String("url", url),
		zap.String("traceId", traceId))

	// 循环检查直播状态，直到获取到origin或status为2
	attempt := 0
	consecutive400Errors := 0 // 连续400错误计数
	maxConsecutive400 := 50   // 最大连续400错误次数

	for {
		// 设置直播API专用的请求头
		liveHeaders := map[string]string{
			"authority":          "weibo.com",
			"referer":            fmt.Sprintf("https://weibo.com/l/wblive/m/show/%s", roomID),
			"sec-ch-ua":          `"Chromium";v="122", "Not(A:Brand";v="24", "Google Chrome";v="122"`,
			"sec-ch-ua-mobile":   "?1",
			"sec-ch-ua-platform": `"Android"`,
			"sec-fetch-dest":     "empty",
			"sec-fetch-mode":     "cors",
			"sec-fetch-site":     "same-origin",
			"x-xsrf-token":       "B25AbAmqEi-nyZ-Vtgfy0b5Y", // 从curl命令中提取的XSRF token
		}

		// 设置直播API专用的cookies - 使用最新的curl命令中的cookies
		liveCookies := "SINAGLOBAL=2214012497427.7573.1741315594816; SCF=Alw3DxACLcj2zHbXW_TrBrcIkRN0739Df2ti1acxd8Y5jgtEeXoEEdm__oNRWflRUhnJmgo31g7mBI_SY6b-HRY.; SUBP=0033WrSXqPxfM725Ws9jqgMF55529P9D9Wh_DS8sJDdgKQ1osv7vbl645JpX5KMhUgL.Foz4S05NSheReKM2dJLoIEXLxK-L1KzL1-2LxK-L1KzL1-2LxK-L1KzL1-2LxK-L1KzL1-2LxKnL12BLBKet; ALF=1756450751; SUB=_2A25FjbLvDeRhGeRH7FIW9C3EyjuIHXVm40onrDV8PUJbkNAbLXP6kW1NTYDNpWqTt5C7Ucy0bZZWF36jfB-CgC2-; XSRF-TOKEN=B25AbAmqEi-nyZ-Vtgfy0b5Y; WBPSESS=I9ynP0BIxiDdMfLOZua1EjtM3LKXfCRH-I5lLoavU1F-5M50kMNtA_YVkGI_4iQBRKau4KRMjkQYgItDO3hFSP2MG8wrZr6G1G81EW_-8aUVKoGMrG5LgR4e_RMy09ngxCRlmZSHeigIG7Sjvxniew==; _s_tentry=weibo.com; Apache=4276989651238.2876.1754364883742; ULV=1754364883800:13:1:1:4276989651238.2876.1754364883742:1753432666707"

		liveData, err := weiboBlogApi.makeHttpRequest(HttpRequestOptions{
			URL:          url,
			Cookies:      liveCookies,
			Timeout:      30 * time.Second,
			UserAgent:    "Mozilla/5.0 (Linux; Android 8.0.0; SM-G955U Build/R16NW) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Mobile Safari/537.36",
			ExtraHeaders: liveHeaders,
			EnableRetry:  false, // 直播监控自己处理重试
		})
		if err != nil {
			// 详细记录错误信息，包括URL和请求参数
			global.GVA_LOG.Warn("获取直播数据失败，继续重试",
				zap.Error(err),
				zap.String("blogID", blogID),
				zap.String("roomID", roomID),
				zap.String("url", url),
				zap.Int("attempt", attempt+1))

			// 如果是HTTP 400错误，可能是认证问题
			if strings.Contains(err.Error(), "400") {
				consecutive400Errors++
				global.GVA_LOG.Warn("HTTP 400错误，可能是认证问题",
					zap.String("blogID", blogID),
					zap.String("roomID", roomID),
					zap.Int("consecutive400Errors", consecutive400Errors))

				// 连续多次400错误，可能是房间不存在或认证完全失效
				if consecutive400Errors >= maxConsecutive400 {
					global.GVA_LOG.Error("连续多次HTTP 400错误，停止监控",
						zap.String("blogID", blogID),
						zap.String("roomID", roomID),
						zap.Int("consecutive400Errors", consecutive400Errors))
					break
				}
				time.Sleep(10 * time.Second) // 延长到10秒
			} else {
				consecutive400Errors = 0 // 重置400错误计数
				time.Sleep(2 * time.Second)
			}
			attempt++
			continue
		}

		// 请求成功，重置400错误计数
		consecutive400Errors = 0

		if liveData == nil {
			global.GVA_LOG.Warn("直播数据为空，继续重试", zap.String("blogID", blogID), zap.Int("attempt", attempt+1))
			time.Sleep(2 * time.Second)
			attempt++
			continue
		}

		// 检查status
		if data, ok := liveData["data"].(map[string]interface{}); ok {
			if item, ok := data["item"].(map[string]interface{}); ok {
				// 检查status是否为2（直播结束）
				if status, exists := item["status"]; exists {
					if statusFloat, ok := status.(float64); ok && statusFloat == 2 {
						global.GVA_LOG.Info("直播已结束", zap.String("blogID", blogID), zap.String("roomID", roomID))
						break
					}
				}

				// 检查是否有origin（直播流地址）
				if streamInfo, ok := item["stream_info"].(map[string]interface{}); ok {
					if replayUrl, ok := streamInfo["replay_url"].(map[string]interface{}); ok {
						if origin, exists := replayUrl["origin"]; exists && origin != nil {
							global.GVA_LOG.Info("获取到直播流地址", zap.String("blogID", blogID), zap.String("origin", fmt.Sprintf("%v", origin)))

							// 将直播数据转换为JSON并存储
							liveBytes, err := json.Marshal(liveData)
							if err != nil {
								global.GVA_LOG.Error("直播数据JSON序列化失败", zap.Error(err), zap.String("blogID", blogID))
								return
							}

							// 更新数据库中的live字段（不包含deleted_at条件）
							err = global.GVA_DB.Unscoped().Model(&weibo.WeiboBlog{}).
								Where("blog_id = ?", blogID).
								Update("live", datatypes.JSON(liveBytes)).Error

							if err != nil {
								global.GVA_LOG.Error("更新直播数据失败", zap.Error(err), zap.String("blogID", blogID))
							}
							return
						}
					}
				}
			}
		}

		// 等待2秒后重试
		time.Sleep(2 * time.Second)
		attempt++
		if attempt%10 == 0 {
			global.GVA_LOG.Info("直播监控中，等待直播流地址或直播结束",
				zap.String("blogID", blogID),
				zap.String("roomID", roomID),
				zap.Int("attempt", attempt),
				zap.Duration("elapsed", time.Duration(attempt*2)*time.Second))
		}
	}

	global.GVA_LOG.Info("直播监控结束", zap.String("blogID", blogID), zap.String("roomID", roomID))
}
