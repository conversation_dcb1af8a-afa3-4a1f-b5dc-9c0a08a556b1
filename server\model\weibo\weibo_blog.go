// 自动生成模板WeiboBlog
package weibo

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"gorm.io/datatypes"
)

// 微博 结构体  WeiboBlog
type WeiboBlog struct {
	global.GVA_MODEL
	Blog_id    string         `json:"blog_id" form:"blog_id" gorm:"uniqueIndex;column:blog_id;" binding:"required"`    //博客ID
	Publish_at time.Time      `json:"publish_at" form:"publish_at" gorm:"index;column:publish_at;" binding:"required"` //发布时间
	Data       datatypes.JSON `json:"data" form:"data" gorm:"column:data;" swaggertype:"object" binding:"required"`    //源数据
	Live       datatypes.JSON `json:"live" form:"live" gorm:"column:live;" swaggertype:"object"`                       //直播数据
	Long_text  datatypes.JSON `json:"long_text" form:"long_text" gorm:"column:long_text;" swaggertype:"object"`        //长消息
}

// TableName 微博 WeiboBlog自定义表名 weibo_blog
func (WeiboBlog) TableName() string {
	return "weibo_blog"
}
