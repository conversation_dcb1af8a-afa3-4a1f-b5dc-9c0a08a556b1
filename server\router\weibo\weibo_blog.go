package weibo

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type WeiboBlogRouter struct{}

// InitWeiboBlogRouter 初始化 微博 路由信息
func (s *WeiboBlogRouter) InitWeiboBlogRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	weiboBlogRouter := Router.Group("weiboBlog").Use(middleware.OperationRecord())
	weiboBlogRouterWithoutRecord := Router.Group("weiboBlog")
	weiboBlogRouterWithoutAuth := PublicRouter.Group("weiboBlog")
	{
		weiboBlogRouter.POST("createWeiboBlog", weiboBlogApi.CreateWeiboBlog)             // 新建微博
		weiboBlogRouter.DELETE("deleteWeiboBlog", weiboBlogApi.DeleteWeiboBlog)           // 删除微博
		weiboBlogRouter.DELETE("deleteWeiboBlogByIds", weiboBlogApi.DeleteWeiboBlogByIds) // 批量删除微博
		weiboBlogRouter.PUT("updateWeiboBlog", weiboBlogApi.UpdateWeiboBlog)              // 更新微博
	}
	{
		weiboBlogRouterWithoutRecord.GET("findWeiboBlog", weiboBlogApi.FindWeiboBlog)       // 根据ID获取微博
		weiboBlogRouterWithoutRecord.GET("getWeiboBlogList", weiboBlogApi.GetWeiboBlogList) // 获取微博列表
		weiboBlogRouterWithoutRecord.GET("fetchLongText", weiboBlogApi.FetchLongText)       // 获取微博长文本
	}
	{
		weiboBlogRouterWithoutAuth.GET("getWeiboBlogPublic", weiboBlogApi.GetWeiboBlogPublic) // 微博开放接口
		weiboBlogRouterWithoutAuth.POST("crawlWeiboData", weiboBlogApi.CrawlWeiboData)        // 抓取微博数据（无需认证）
	}
}
