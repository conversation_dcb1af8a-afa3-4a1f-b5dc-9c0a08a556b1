package task

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
)

// WeiboCrawlerTask 微博抓取任务
type WeiboCrawlerTask struct {
	UID     string
	Cookies string
	running bool
	mutex   sync.RWMutex
}

// NewWeiboCrawlerTask 创建微博抓取任务
func NewWeiboCrawlerTask(uid, cookies string) *WeiboCrawlerTask {
	return &WeiboCrawlerTask{
		UID:     uid,
		Cookies: cookies,
		running: false,
	}
}

// Run 实现cron.Job接口 - 调用现有的抓取接口
func (w *WeiboCrawlerTask) Run() {
	w.mutex.Lock()
	if w.running {
		global.GVA_LOG.Info("微博抓取任务正在运行，跳过本次执行", zap.String("uid", w.UID))
		w.mutex.Unlock()
		return
	}
	w.running = true
	w.mutex.Unlock()

	defer func() {
		w.mutex.Lock()
		w.running = false
		w.mutex.Unlock()
	}()

	// 直接调用现有的抓取接口
	err := w.callCrawlAPI()
	if err != nil {
		global.GVA_LOG.Error("微博抓取任务执行失败", zap.String("uid", w.UID), zap.Error(err))
	}
}

// callCrawlAPI 调用现有的抓取接口
func (w *WeiboCrawlerTask) callCrawlAPI() error {
	// 构建请求URL
	baseURL := "http://localhost:8080/api/weiboBlog/crawlWeiboData"
	params := url.Values{}
	params.Add("uid", w.UID)
	if w.Cookies != "" {
		params.Add("cookies", w.Cookies)
	}

	requestURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())

	// 创建HTTP请求
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", requestURL, nil)
	if err != nil {
		return fmt.Errorf("创建请求失败: %v", err)
	}

	// 添加认证头（如果需要）
	// req.Header.Set("Authorization", "Bearer your-token")

	// 发送请求
	client := &http.Client{Timeout: 30 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("请求返回错误状态码: %d", resp.StatusCode)
	}
	return nil
}
